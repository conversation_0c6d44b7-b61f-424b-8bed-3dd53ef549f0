package ir.rahavardit.ariel.utils

import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream

/**
 * Utility class for file operations.
 */
object FileUtils {

    /**
     * Converts a content URI to a File.
     *
     * @param uri The URI to convert.
     * @param context The context to use for accessing the content resolver.
     * @return The converted File.
     */
    fun uriToFile(uri: Uri, context: Context): File {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        val nameIndex = cursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME)
        cursor?.moveToFirst()
        val fileName = cursor?.getString(nameIndex ?: 0) ?: "file"
        cursor?.close()

        val tempFile = File(context.cacheDir, fileName)
        tempFile.createNewFile()

        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            FileOutputStream(tempFile).use { outputStream ->
                inputStream.copyTo(outputStream)
            }
        }

        return tempFile
    }

    /**
     * Creates a MultipartBody.Part from a file URI.
     *
     * @param uri The URI of the file.
     * @param context The context to use for accessing the content resolver.
     * @param partName The name of the form field.
     * @return The MultipartBody.Part for the file.
     */
    fun createFilePart(uri: Uri, context: Context, partName: String = "file"): MultipartBody.Part {
        val file = uriToFile(uri, context)
        val mimeType = context.contentResolver.getType(uri) ?: "application/octet-stream"
        val requestFile = file.asRequestBody(mimeType.toMediaTypeOrNull())
        return MultipartBody.Part.createFormData(partName, file.name, requestFile)
    }
}
