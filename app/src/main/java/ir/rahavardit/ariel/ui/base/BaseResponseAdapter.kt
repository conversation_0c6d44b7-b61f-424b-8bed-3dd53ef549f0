package ir.rahavardit.ariel.ui.base

import android.content.Intent
import android.net.Uri
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.LifecycleCoroutineScope
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.TicketResponse
import ir.rahavardit.ariel.data.model.UserSignature
import ir.rahavardit.ariel.data.repository.TicketRepository
import ir.rahavardit.ariel.utils.HtmlRenderer
import kotlinx.coroutines.launch

/**
 * Utility class for ticket response adapters containing common functionality.
 */
class BaseResponseAdapter {
    
    private val ticketRepository = TicketRepository()

    /**
     * Fetches and displays user signature for superusers and limited admins.
     */
    fun fetchUserSignature(
        lifecycleScope: LifecycleCoroutineScope,
        context: android.content.Context,
        shortUuid: String,
        signatureTextView: TextView
    ) {
        val sessionManager = SessionManager(context)
        val token = sessionManager.getAuthToken() ?: return

        lifecycleScope.launch {
            try {
                val result = ticketRepository.getUserSignature(token, shortUuid)

                result.fold(
                    onSuccess = { signature ->
                        displaySignature(signature, signatureTextView)
                    },
                    onFailure = {
                        // Silently fail - signature is optional
                    }
                )
            } catch (e: Exception) {
                // Silently fail - signature is optional
            }
        }
    }
    
    /**
     * Displays the user signature in the provided TextView.
     */
    private fun displaySignature(signature: UserSignature, signatureTextView: TextView) {
        if (signature.body.isNotBlank()) {
            HtmlRenderer.applyToTextView(signatureTextView, signature.body)
            signatureTextView.visibility = View.VISIBLE
        } else {
            signatureTextView.visibility = View.GONE
        }
    }
    
    /**
     * Opens a file URL in the default browser/app.
     */
    private fun openFileUrl(context: android.content.Context, fileUrl: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(fileUrl))
            context.startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(
                context,
                context.getString(R.string.error_opening_file),
                Toast.LENGTH_SHORT
            ).show()
        }
    }
    
    /**
     * Sets up file attachment UI for a response.
     */
    fun setupFileAttachment(
        response: TicketResponse,
        fileLayout: View,
        fileNameTextView: TextView,
        downloadButton: View,
        context: android.content.Context
    ) {
        if (response.hasFile && response.file != null) {
            fileLayout.visibility = View.VISIBLE
            fileNameTextView.text = response.file.substringAfterLast('/')
            
            downloadButton.setOnClickListener {
                openFileUrl(context, response.file)
            }
        } else {
            fileLayout.visibility = View.GONE
        }
    }
}
