package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.IncomeObject
import java.text.NumberFormat
import java.util.Locale

class IncomeAdapter(
    private var incomeList: List<IncomeObject>,
    private val onDeleteClick: (String) -> Unit,
    private val onEditClick: (String, IncomeObject) -> Unit
) : RecyclerView.Adapter<IncomeAdapter.IncomeViewHolder>() {

    class IncomeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IncomeViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_income, parent, false)
        return IncomeViewHolder(view)
    }

    override fun onBindViewHolder(holder: IncomeViewHolder, position: Int) {
        val income = incomeList[position]

        holder.textShortUuid.text = income.shortUuid
        holder.textBank.text = income.bankInfo?.title ?: "نامشخص"
        holder.textCreated.text = income.slashedDatePersian

        // Handle title - hide if empty or null
        if (!income.title.isNullOrEmpty()) {
            holder.textTitle.text = income.title
            holder.textTitle.visibility = android.view.View.VISIBLE
        } else {
            holder.textTitle.visibility = android.view.View.GONE
        }

        holder.textCategory.text = "دسته‌بندی: ${income.categoryInfo?.title ?: "نامشخص"}"

        // Handle tags - hide if empty
        if (income.tagsNames?.isNotEmpty() == true) {
            holder.textTags.text = "برچسب‌ها: ${income.tagsNames.joinToString(", ")}"
            holder.textTags.visibility = android.view.View.VISIBLE
        } else {
            holder.textTags.visibility = android.view.View.GONE
        }

        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(income.amount)} تومان"

        // Setup menu button click listener
        holder.btnMenu.setOnClickListener { view ->
            val popup = PopupMenu(view.context, view)
            popup.menuInflater.inflate(R.menu.item_menu, popup.menu)
            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_edit -> {
                        onEditClick(income.shortUuid, income)
                        true
                    }
                    R.id.action_delete -> {
                        showDeleteConfirmationDialog(view, income.shortUuid)
                        true
                    }
                    else -> false
                }
            }
            popup.show()
        }
    }

    override fun getItemCount(): Int = incomeList.size

    fun updateData(newIncomeList: List<IncomeObject>) {
        incomeList = newIncomeList
        notifyDataSetChanged()
    }

    /**
     * Shows a confirmation dialog before deleting the income item.
     */
    private fun showDeleteConfirmationDialog(view: View, shortUuid: String) {
        val dialogView = LayoutInflater.from(view.context).inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = view.context.getString(R.string.delete_transaction_confirmation_title)
        messageTextView.text = view.context.getString(R.string.delete_transaction_confirmation_message)

        val dialog = AlertDialog.Builder(view.context)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            onDeleteClick(shortUuid)
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
}
