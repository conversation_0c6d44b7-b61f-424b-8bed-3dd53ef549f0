package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.EventObject

class EventAdapter(
    private var eventList: List<EventObject>
) : RecyclerView.Adapter<EventAdapter.EventViewHolder>() {

    class EventViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textDate: TextView = itemView.findViewById(R.id.text_date)
    }

    override fun onCreateViewHolder(parent: <PERSON><PERSON><PERSON>, viewType: Int): EventViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event, parent, false)
        return EventViewHolder(view)
    }

    override fun onBindViewHolder(holder: EventViewHolder, position: Int) {
        val event = eventList[position]
        
        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textDate.text = event.slashedDatePersian
    }

    override fun getItemCount(): Int = eventList.size

    fun updateData(newEventList: List<EventObject>) {
        eventList = newEventList
        notifyDataSetChanged()
    }
}
