package ir.rahavardit.ariel.ui.edittransaction

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.BankResponse
import ir.rahavardit.ariel.data.model.NewTransactionRequest
import ir.rahavardit.ariel.data.model.TagResponse
import ir.rahavardit.ariel.data.model.TransactionCategoryResponse
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.data.repository.TransactionRepository
import kotlinx.coroutines.launch

class EditTransactionViewModel : ViewModel() {

    private val transactionRepository = TransactionRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    private val _modes = MutableLiveData<List<TransactionMode>>()
    val modes: LiveData<List<TransactionMode>> = _modes

    private val _banks = MutableLiveData<BankResponse>()
    val banks: LiveData<BankResponse> = _banks

    private val _categories = MutableLiveData<TransactionCategoryResponse>()
    val categories: LiveData<TransactionCategoryResponse> = _categories

    private val _tags = MutableLiveData<TagResponse>()
    val tags: LiveData<TagResponse> = _tags

    private val _transactionUpdateResult = MutableLiveData<TransactionUpdateResult>()
    val transactionUpdateResult: LiveData<TransactionUpdateResult> = _transactionUpdateResult

    sealed class TransactionUpdateResult {
        data class Success(val transaction: TransactionResponse) : TransactionUpdateResult()
        data class Error(val errorMessage: String) : TransactionUpdateResult()
    }

    data class ValidationResult(
        val isDateValid: Boolean,
        val isAmountValid: Boolean,
        val isModeValid: Boolean,
        val isCategoryValid: Boolean
    )

    fun loadModes(token: String) {
        viewModelScope.launch {
            _isLoading.value = true
            transactionRepository.getTransactionModes(token).fold(
                onSuccess = { modes ->
                    _modes.value = modes
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load modes"
                }
            )
            _isLoading.value = false
        }
    }

    fun loadBanks(token: String) {
        viewModelScope.launch {
            _isLoading.value = true
            transactionRepository.getBanks(token).fold(
                onSuccess = { banks ->
                    _banks.value = banks
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load banks"
                }
            )
            _isLoading.value = false
        }
    }

    fun loadCategories(token: String) {
        viewModelScope.launch {
            _isLoading.value = true
            transactionRepository.getTransactionCategories(token).fold(
                onSuccess = { categories ->
                    _categories.value = categories
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load categories"
                }
            )
            _isLoading.value = false
        }
    }

    fun loadTags(token: String) {
        viewModelScope.launch {
            _isLoading.value = true
            transactionRepository.getTags(token).fold(
                onSuccess = { tags ->
                    _tags.value = tags
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load tags"
                }
            )
            _isLoading.value = false
        }
    }

    fun validateInputs(
        date: String?,
        amount: String,
        mode: String?,
        categoryId: String?
    ): ValidationResult {
        return ValidationResult(
            isDateValid = !date.isNullOrBlank(),
            isAmountValid = amount.isNotBlank() && amount.toIntOrNull() != null && amount.toInt() > 0,
            isModeValid = !mode.isNullOrBlank(),
            isCategoryValid = !categoryId.isNullOrBlank()
        )
    }

    fun updateTransaction(
        token: String,
        shortUuid: String,
        date: String,
        title: String?,
        amount: Int,
        mode: String,
        bankId: Int?,
        categoryId: Int,
        tagIds: List<Int>
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = ""

            val request = NewTransactionRequest(
                date = date,
                title = title,
                amount = amount,
                mode = mode,
                bank = bankId?.toString(),
                category = categoryId.toString(),
                tags = tagIds
            )

            transactionRepository.updateTransaction(token, shortUuid, request).fold(
                onSuccess = { transaction ->
                    _transactionUpdateResult.value = TransactionUpdateResult.Success(transaction)
                },
                onFailure = { exception ->
                    _transactionUpdateResult.value = TransactionUpdateResult.Error(
                        exception.message ?: "Failed to update transaction"
                    )
                }
            )
            _isLoading.value = false
        }
    }
}
