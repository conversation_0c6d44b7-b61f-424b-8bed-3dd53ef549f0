package ir.rahavardit.ariel.ui.base

import android.os.Bundle
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.utils.setupPagination

/**
 * Base fragment for handling paginated lists with common functionality.
 */
abstract class BasePaginatedFragment<T, VH : RecyclerView.ViewHolder> : Fragment() {

    protected lateinit var sessionManager: SessionManager
    protected abstract val viewModel: BasePaginatedViewModel<T>
    protected abstract val adapter: ListAdapter<T, VH>
    protected abstract val recyclerView: RecyclerView
    protected abstract val progressBar: ProgressBar
    protected abstract val progressBarPagination: ProgressBar
    protected abstract val tvError: TextView
    protected abstract val tvEmpty: TextView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        observeViewModel()
        fetchData()
        setupBackNavigation()
    }

    /**
     * Sets up the RecyclerView with pagination.
     */
    protected open fun setupRecyclerView() {
        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>

            // Setup pagination using extension function
            setupPagination(
                onLoadMore = { loadMoreData() },
                hasMorePages = { viewModel.hasMorePages() },
                isLoadingMore = { viewModel.isLoadingMore.value ?: false }
            )
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    protected open fun observeViewModel() {
        viewModel.items.observe(viewLifecycleOwner) { items ->
            adapter.submitList(items)

            // Show empty view if the list is empty
            if (items.isEmpty()) {
                tvEmpty.visibility = View.VISIBLE
                recyclerView.visibility = View.GONE
            } else {
                tvEmpty.visibility = View.GONE
                recyclerView.visibility = View.VISIBLE
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                tvError.visibility = View.GONE
                tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                tvError.text = errorMessage
                tvError.visibility = View.VISIBLE
                recyclerView.visibility = View.GONE
                tvEmpty.visibility = View.GONE
            } else {
                tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Sets up back navigation handling.
     */
    protected open fun setupBackNavigation() {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    findNavController().navigateUp()
                }
            }
        )
    }

    /**
     * Fetches data from the API.
     */
    protected open fun fetchData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchItems(token)
        } else {
            tvError.text = getString(R.string.authentication_token_not_found)
            tvError.visibility = View.VISIBLE
            recyclerView.visibility = View.GONE
        }
    }

    /**
     * Loads more data for pagination.
     */
    protected open fun loadMoreData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreItems(token)
        }
    }
}
