package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a transaction mode option.
 */
data class TransactionMode(
    val value: String,
    val label: String
)

/**
 * Data class representing a bank response.
 */
data class BankResponse(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<BankItem>
)

/**
 * Data class representing a bank item.
 */
data class BankItem(
    val id: Int,
    val title: String,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    val updated: String
)

/**
 * Data class representing a category response for transactions.
 */
data class TransactionCategoryResponse(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<TransactionCategoryItem>
)

/**
 * Data class representing a category item for transactions.
 */
data class TransactionCategoryItem(
    val id: Int,
    val title: String,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    val updated: String
)

/**
 * Data class representing a tag response.
 */
data class TagResponse(
    val count: Int,
    val next: String?,
    val previous: String?,
    val results: List<TagItem>
)

/**
 * Data class representing a tag item.
 */
data class TagItem(
    val id: Int,
    val title: String,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    val updated: String
)

/**
 * Data class representing a new transaction request.
 */
data class NewTransactionRequest(
    val date: String, // Format: YYYY/MM/DD
    val title: String?,
    val amount: Int,
    val mode: String,
    val bank: String?, // id as string
    val category: String, // id as string
    val tags: List<Int> // List of ids
)

/**
 * Data class representing a transaction response.
 */
data class TransactionResponse(
    val id: Int,
    val mode: String,
    val title: String?,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    val bank: BankItem?,
    val category: TransactionCategoryItem,
    val tags: List<Int>,
    @SerializedName("tags_names")
    val tagsNames: List<String>,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    val updated: String
)
