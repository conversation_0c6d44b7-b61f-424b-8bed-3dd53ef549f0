package ir.rahavardit.ariel.data.repository

import android.util.Log
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.PasswordChangeRequest
import ir.rahavardit.ariel.data.model.Profile
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles profile-related operations.
 */
class ProfileRepository {

    private val apiService = RetrofitClient.getApiService()
    private val TAG = "ProfileRepository"

    /**
     * Retrieves the profile information for the authenticated user.
     *
     * @param token The authentication token.
     * @return A Result containing either the profile information or an Exception.
     */
    suspend fun getProfile(token: String): Result<Profile> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getProfile(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch profile: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Changes the password for the authenticated user.
     *
     * @param token The authentication token.
     * @param oldPassword The current password of the user.
     * @param newPassword1 The new password.
     * @param newPassword2 The confirmation of the new password.
     * @return A Result indicating success or failure.
     */
    suspend fun changePassword(
        token: String,
        oldPassword: String,
        newPassword1: String,
        newPassword2: String
    ): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val passwordChangeRequest = PasswordChangeRequest(
                    oldPassword = oldPassword,
                    newPassword1 = newPassword1,
                    newPassword2 = newPassword2
                )

                // Log.d(TAG, "Sending password change request")
                val response = apiService.changePassword(authToken, passwordChangeRequest)

                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    val errorMessage = when (response.code()) {
                        400 -> "Invalid password data. Please check your inputs."
                        401 -> "Authentication failed. Please log in again."
                        403 -> "You don't have permission to change the password."
                        else -> "Failed to change password: ${response.message()}"
                    }
                    Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error changing password", e)
                Result.failure(e)
            }
        }
    }
}
