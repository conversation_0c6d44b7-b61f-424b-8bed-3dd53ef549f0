package ir.rahavardit.ariel.data.repository

import android.content.Context
import android.net.Uri
import android.util.Log
import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.CategoryResponse
import ir.rahavardit.ariel.data.model.CategoryUpdateRequest
import ir.rahavardit.ariel.data.model.PaginatedResponse
import ir.rahavardit.ariel.data.model.Priority
import ir.rahavardit.ariel.data.model.PriorityUpdateRequest
import ir.rahavardit.ariel.data.model.RatingRequest
import ir.rahavardit.ariel.data.model.Status
import ir.rahavardit.ariel.data.model.StatusUpdateRequest
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.data.model.UserSignature
import ir.rahavardit.ariel.utils.FileUtils
import ir.rahavardit.ariel.utils.TokenUtils
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Repository class that handles ticket-related operations.
 */
class TicketRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    suspend fun getMyTickets(token: String, pageSize: Int = 15): Result<PaginatedResponse<Ticket>> {
        return safeApiCall {
            apiService.getMyTickets(TokenUtils.formatToken(token), pageSize)
        }
    }

    suspend fun getTicketsFromUrl(token: String, url: String): Result<PaginatedResponse<Ticket>> {
        return safeApiCall {
            apiService.getTicketsFromUrl(TokenUtils.formatToken(token), url)
        }
    }

    suspend fun getMyTicketsByStatus(token: String, status: String): Result<PaginatedResponse<Ticket>> {
        return safeApiCall {
            apiService.getMyTicketsByStatus(TokenUtils.formatToken(token), status)
        }
    }

    suspend fun getMyTicketsByGroupId(token: String, groupId: Int): Result<PaginatedResponse<Ticket>> {
        return safeApiCall {
            apiService.getMyTicketsByGroupId(TokenUtils.formatToken(token), groupId)
        }
    }

    suspend fun getTicketDetails(token: String, shortUuid: String): Result<Ticket> {
        return safeApiCall {
            apiService.getTicketDetails(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun replyToTicket(token: String, shortUuid: String, message: String): Result<Ticket> {
        return safeApiCall {
            val messageMap = mapOf("message" to message)
            apiService.replyToTicket(TokenUtils.formatToken(token), shortUuid, messageMap)
        }
    }

    suspend fun replyToTicketWithFile(
        token: String,
        shortUuid: String,
        message: String,
        fileUri: Uri,
        context: Context
    ): Result<Ticket> {
        return safeApiCall {
            // Create request parts
            val messageRequestBody = message.toRequestBody("text/plain".toMediaTypeOrNull())
            val filePart = FileUtils.createFilePart(fileUri, context)

            // Make API call
            apiService.replyToTicketWithFile(
                TokenUtils.formatToken(token),
                shortUuid,
                messageRequestBody,
                filePart
            )
        }
    }



    suspend fun getUserSignature(token: String, shortUuid: String): Result<UserSignature> {
        return safeApiCall {
            apiService.getUserSignature(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun getCategories(token: String): Result<List<CategoryResponse>> {
        return safeApiCall {
            apiService.getCategories(TokenUtils.formatToken(token))
        }.map { it.results }
    }

    /**
     * Updates the category of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param categoryId The ID of the new category.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketCategory(token: String, shortUuid: String, categoryId: Int): Result<Ticket> {
        return safeApiCall {
            val categoryRequest = CategoryUpdateRequest(categoryId)
            apiService.setTicketCategory(TokenUtils.formatToken(token), shortUuid, categoryRequest)
        }
    }

    suspend fun getPriorities(token: String): Result<List<Priority>> {
        return safeApiCall {
            apiService.getPriorities(TokenUtils.formatToken(token))
        }
    }

    /**
     * Updates the priority of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param priorityValue The value of the new priority.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketPriority(token: String, shortUuid: String, priorityValue: String): Result<Ticket> {
        return safeApiCall {
            val priorityRequest = PriorityUpdateRequest(priorityValue)
            apiService.setTicketPriority(TokenUtils.formatToken(token), shortUuid, priorityRequest)
        }
    }

    suspend fun getStatuses(token: String): Result<List<Status>> {
        return safeApiCall {
            apiService.getStatuses(TokenUtils.formatToken(token))
        }
    }

    /**
     * Updates the status of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param statusValue The value of the new status.
     * @return A Result containing either the updated ticket or an Exception.
     */
    suspend fun setTicketStatus(token: String, shortUuid: String, statusValue: String): Result<Ticket> {
        return safeApiCall {
            val statusRequest = StatusUpdateRequest(statusValue)
            apiService.setTicketStatus(TokenUtils.formatToken(token), shortUuid, statusRequest)
        }
    }

    suspend fun deleteTicket(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteTicket(TokenUtils.formatToken(token), shortUuid)
        }
    }

    /**
     * Retrieves the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of groups or an Exception.
     */
    suspend fun getUserGroups(token: String, shortUuid: String): Result<List<String>> {
        return safeApiCall {
            apiService.getUserGroups(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun getUserFullName(token: String, shortUuid: String): Result<String> {
        return safeApiCall {
            apiService.getUserFullName(TokenUtils.formatToken(token), shortUuid)
        }
    }

    suspend fun rateTicket(token: String, shortUuid: String, rating: Int): Result<Ticket> {
        return safeApiCall {
            val ratingRequest = RatingRequest(rating)
            apiService.rateTicket(TokenUtils.formatToken(token), shortUuid, ratingRequest)
        }
    }
}
