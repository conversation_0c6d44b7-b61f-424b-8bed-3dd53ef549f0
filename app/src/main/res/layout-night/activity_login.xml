<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_dark"
    android:fillViewport="true"
    tools:context=".ui.login.LoginActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="24dp">

        <!-- Top section with logo and app name -->
        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="48dp"
            android:contentDescription="@string/login__app_logo"
            android:elevation="4dp"
            android:src="@mipmap/ic_launcher"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/app_name_persian"
            android:textColor="@color/primary_light"
            android:textSize="32sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_logo" />

        <TextView
            android:id="@+id/tv_welcome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/login__subtitle"
            android:textColor="@color/white"
            android:textSize="17sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_app_name" />



    <!-- Login form section below welcome message -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="32dp"
        app:cardBackgroundColor="#1E1E1E"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_welcome">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_username"
                style="@style/Widget.App.TextInputLayout.Dark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/login__username"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_username"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_password"
                style="@style/Widget.App.TextInputLayout.Dark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/login__password"
                app:endIconMode="password_toggle"
                app:startIconDrawable="@drawable/ic_lock">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_login"
                style="@style/Widget.App.Button.Dark"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="24dp"
                android:text="@string/login__login" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_biometric_login"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="10dp"
                android:text="@string/use_biometric_login"
                android:visibility="gone"
                app:icon="@drawable/ic_fingerprint"
                app:iconGravity="textEnd"
                style="@style/Widget.App.Button.Outlined" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/primary_light"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
