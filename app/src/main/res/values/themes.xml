<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Ariel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">

        <!-- Font family and size -->
        <item name="android:fontFamily">@font/vazir_font_family</item>
        <item name="android:textSize">13sp</item>

        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Surface and background colors -->
        <item name="colorSurface">@color/surface</item>
        <item name="android:colorBackground">@color/background_light</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <!-- Customize your theme here. -->
        <item name="materialButtonStyle">@style/Widget.App.Button</item>
        <item name="textInputStyle">@style/Widget.App.TextInputLayout</item>
        <item name="navigationViewStyle">@style/Widget.App.NavigationView</item>
    </style>

    <style name="Theme.Ariel.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.Ariel.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.Ariel.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- Custom button style -->
    <style name="Widget.App.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="elevation">4dp</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="backgroundTint">@color/primary</item>
    </style>

    <!-- Custom outlined button style for biometric login -->
    <style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:textColor">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="iconTint">@color/primary</item>
    </style>

    <!-- Custom text input style -->
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="boxBackgroundColor">@color/background_light</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="startIconTint">@color/primary</item>
        <item name="endIconTint">@color/primary</item>
    </style>

    <!-- Custom NavigationView style -->
    <style name="Widget.App.NavigationView" parent="Widget.MaterialComponents.NavigationView">
        <item name="itemTextAppearance">@style/TextAppearance.App.NavigationView</item>
    </style>

    <!-- Custom text appearance for NavigationView items -->
    <style name="TextAppearance.App.NavigationView" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:fontFamily">@font/vazir_font_family</item>
        <item name="android:textSize">14sp</item>
    </style>
</resources>
