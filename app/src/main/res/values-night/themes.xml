<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Ariel" parent="Theme.MaterialComponents.DayNight.DarkActionBar">

        <!-- Font family and size -->
        <item name="android:fontFamily">@font/vazir_font_family</item>
        <item name="android:textSize">13sp</item>

        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryVariant">@color/primary</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorSecondaryVariant">@color/secondary</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Surface and background colors -->
        <item name="colorSurface">@color/surface_dark</item>
        <item name="android:colorBackground">@color/surface_dark</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/gray</item>
        <!-- Customize your theme here. -->
        <item name="materialButtonStyle">@style/Widget.App.Button.Dark</item>
        <item name="textInputStyle">@style/Widget.App.TextInputLayout.Dark</item>
        <item name="navigationViewStyle">@style/Widget.App.NavigationView.Dark</item>
    </style>

    <style name="Theme.Ariel.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.Ariel.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.Ariel.PopupOverlay" parent="ThemeOverlay.AppCompat.Dark" />

    <!-- Custom button style for dark theme -->
    <style name="Widget.App.Button.Dark" parent="Widget.MaterialComponents.Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="elevation">4dp</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Custom outlined button style for biometric login in dark theme -->
    <style name="Widget.App.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">8dp</item>
        <item name="strokeColor">@color/primary_light</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/primary_light</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="iconTint">@color/primary_light</item>
    </style>

    <!-- Custom text input style for dark theme -->
    <style name="Widget.App.TextInputLayout.Dark" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="boxBackgroundColor">#2D2D2D</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="hintTextColor">@color/primary_light</item>
        <item name="startIconTint">@color/primary_light</item>
        <item name="endIconTint">@color/primary_light</item>
    </style>

    <!-- Custom NavigationView style for dark theme -->
    <style name="Widget.App.NavigationView.Dark" parent="Widget.MaterialComponents.NavigationView">
        <item name="itemTextAppearance">@style/TextAppearance.App.NavigationView.Dark</item>
    </style>

    <!-- Custom text appearance for NavigationView items in dark theme -->
    <style name="TextAppearance.App.NavigationView.Dark" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:fontFamily">@font/vazir_font_family</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/white</item>
    </style>
</resources>
