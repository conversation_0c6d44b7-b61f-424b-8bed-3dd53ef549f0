<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="24dp">

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="انتخاب تاریخ"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="?attr/colorOnSurface"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Date Pickers Container -->
    <LinearLayout
        android:id="@+id/date_pickers_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Year Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="سال"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/year_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

        <!-- Month Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ماه"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/month_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

        <!-- Day Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="روز"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/day_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Buttons -->
    <LinearLayout
        android:id="@+id/buttons_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="end"
        app:layout_constraintTop_toBottomOf="@id/date_pickers_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="لغو" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_ok"
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="تأیید" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
