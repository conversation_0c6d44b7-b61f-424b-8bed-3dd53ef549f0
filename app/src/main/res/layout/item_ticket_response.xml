<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp"
    app:strokeColor="@color/card_border"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_response_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="2fdc0e80" />

        <TextView
            android:id="@+id/tv_separator1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:text="•"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintStart_toEndOf="@id/tv_response_id"
            app:layout_constraintTop_toTopOf="@id/tv_response_id"
            app:layout_constraintBottom_toBottomOf="@id/tv_response_id" />

        <TextView
            android:id="@+id/tv_response_author"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintStart_toEndOf="@id/tv_separator1"
            app:layout_constraintTop_toTopOf="@id/tv_response_id"
            app:layout_constraintBottom_toBottomOf="@id/tv_response_id"
            tools:text="rozhan" />

        <TextView
            android:id="@+id/tv_response_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="?android:attr/textColorSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="یک‌شنبه ۰۶:۳۴:۴۷ ۱۴۰۳/۰۳/۱۳" />

        <ImageView
            android:id="@+id/iv_admin_badge"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="4dp"
            android:src="@android:drawable/btn_star"
            android:tint="@color/admin_color"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/tv_response_author"
            app:layout_constraintStart_toEndOf="@id/tv_response_author"
            app:layout_constraintTop_toTopOf="@id/tv_response_author"
            tools:visibility="visible" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="?android:attr/listDivider"
            app:layout_constraintTop_toBottomOf="@id/tv_response_id" />

        <TextView
            android:id="@+id/tv_response_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="?attr/textAppearanceBody1"
            android:autoLink="web"
            android:linksClickable="true"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:text="طرفدارترین زلیخاوار بهینه‌سازهای ضددولتی گردنفرازان کمیت‌ها حطاما چشمه‌ساری وذاتش الحصار منگک دشنه‌ی میرآب. حجاب‌ها ابوالسعادات فروخواند حکیم‌الملک! رودیونویچ ناتوریست! حاضرمثال انداره‌گیری اتهامی!" />

        <!-- User Signature Section -->
        <TextView
            android:id="@+id/tv_user_signature"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textStyle="italic"
            android:textColor="?android:attr/textColorSecondary"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/tv_response_message"
            tools:text="مجموعه‌تلویزیونی كرش؟\n کرشمه‌ساز گلدشت‌بن؟."
            tools:visibility="visible" />

        <!-- File Attachment Section -->
        <LinearLayout
            app:layout_constraintTop_toBottomOf="@id/tv_user_signature"
            android:id="@+id/layout_response_file"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_response_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textAppearance="?attr/textAppearanceBody1"
                tools:text="document.pdf" />

            <Button
                android:id="@+id/btn_response_download_file"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/download_file"
                app:icon="@android:drawable/ic_menu_save" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
