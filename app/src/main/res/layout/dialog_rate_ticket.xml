<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Icon -->
    <ImageView
        android:id="@+id/iv_rating_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_star_rate"
        android:tint="?attr/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/rate_ticket"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:textColor="?attr/colorOnSurface"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_rating_icon" />

    <!-- Subtitle -->
    <TextView
        android:id="@+id/tv_dialog_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:alpha="0.7"
        android:gravity="center"
        android:lineSpacingExtra="2dp"
        android:text="@string/rate_ticket_subtitle"
        android:textAppearance="?attr/textAppearanceBody2"
        android:textColor="?attr/colorOnSurface"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_title" />

    <!-- Rating Container -->
    <!-- android:background="@drawable/rating_background" -->
    <LinearLayout
        android:id="@+id/rating_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_marginTop="12dp"
        android:paddingHorizontal="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_dialog_subtitle">

        <RatingBar
            android:id="@+id/rating_bar_dialog"
            style="@style/Widget.AppCompat.RatingBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:numStars="5"
            android:progressTint="@color/rating_star_filled"
            android:rating="0"
            android:secondaryProgressTint="@color/rating_star_empty"
            android:stepSize="1" />

    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginTop="16dp"
        android:indeterminateTint="?attr/colorPrimary"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rating_container" />

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="12dp"
        android:alpha="0.12"
        android:background="?attr/colorOnSurface"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progress_bar" />

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/layout_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="end"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/cancel"
            android:textColor="?attr/colorOnSurface" />

        <Button
            android:id="@+id/btn_send"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:backgroundTint="?attr/colorPrimary"
            android:text="@string/submit"
            android:textColor="@android:color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
