<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp" >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="12dp"
        android:paddingBottom="20dp">

        <!-- Status Indicator -->
        <View
            android:id="@+id/status_indicator"
            android:layout_width="3dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:background="?attr/colorPrimary"
            android:alpha=".3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/tv_ticket_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textAppearance="?attr/textAppearanceSubtitle2"
            android:textColor="?attr/colorOnSurface"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/status_indicator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Ticket Title That Might Be Long" />

        <!-- Chips -->
        <LinearLayout
            android:id="@+id/ll_chips_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toStartOf="@id/status_indicator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_ticket_title">

            <!-- Priority -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:textSize="10sp"
                app:chipMinHeight="22dp"
                app:chipCornerRadius="4dp"
                app:chipStartPadding="6dp"
                app:chipEndPadding="6dp"
                tools:text="خیلی زیاد" />

            <!-- Status -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:textSize="10sp"
                app:chipMinHeight="22dp"
                app:chipCornerRadius="4dp"
                app:chipStartPadding="6dp"
                app:chipEndPadding="6dp"
                tools:text="در انتظار پاسخ" />

            <!-- Category -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                app:chipMinHeight="22dp"
                app:chipCornerRadius="4dp"
                app:chipStartPadding="6dp"
                app:chipEndPadding="6dp"
                tools:text="Software" />

        </LinearLayout>

        <!-- Divider Line -->
        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:background="@color/divider_color"
            app:layout_constraintEnd_toStartOf="@id/status_indicator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_chips_container" />

        <!-- Date icon -->
        <ImageView
            android:id="@+id/iv_date_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="16dp"
            android:src="@drawable/ic_calendar"
            android:tint="?android:attr/textColorSecondary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:ignore="UseAppTint" />

        <!-- Date -->
        <TextView
            android:id="@+id/tv_created_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="@id/iv_date_icon"
            app:layout_constraintEnd_toStartOf="@id/cv_ticket_id"
            app:layout_constraintStart_toEndOf="@id/iv_date_icon"
            app:layout_constraintTop_toTopOf="@id/iv_date_icon"
            tools:text="یک‌شنبه ۰۰:۱۸:۲۹ ۱۴۰۳/۰۳/۲۷" />

        <!-- Short UUID Chip -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cv_ticket_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            app:cardBackgroundColor="@color/short_uuid_chip_bg"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_date_icon"
            app:layout_constraintEnd_toStartOf="@id/status_indicator"
            app:layout_constraintTop_toTopOf="@id/iv_date_icon">

            <TextView
                android:id="@+id/tv_ticket_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="6dp"
                android:paddingVertical="3dp"
                android:textAppearance="?attr/textAppearanceCaption"
                android:textColor="@color/short_uuid_chip_fg"
                android:textSize="10sp"
                tools:text="4588e326" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Author -->
        <TextView
            android:id="@+id/tv_ticket_author"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_marginTop="0dp"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="10sp"
            app:layout_constraintEnd_toStartOf="@+id/cv_ticket_id"
            app:layout_constraintBottom_toBottomOf="@id/iv_date_icon"
            app:layout_constraintTop_toTopOf="@id/iv_date_icon"
            tools:text="rozhan" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
